[{"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\can.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\can.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\can.d .\\..\\Core\\Src\\can.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\canopen.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\canopen.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\canopen.d .\\..\\Core\\Src\\canopen.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\gpio.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\gpio.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\gpio.d .\\..\\Core\\Src\\gpio.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\iap.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\iap.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\iap.d .\\..\\Core\\Src\\iap.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\main.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\main.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\main.d .\\..\\Core\\Src\\main.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\stm32f1xx_hal_msp.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\stm32f1xx_hal_msp.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\stm32f1xx_hal_msp.d .\\..\\Core\\Src\\stm32f1xx_hal_msp.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\stm32f1xx_it.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\stm32f1xx_it.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\stm32f1xx_it.d .\\..\\Core\\Src\\stm32f1xx_it.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\stmflash.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\stmflash.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\stmflash.d .\\..\\Core\\Src\\stmflash.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\sys.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\sys.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\sys.d .\\..\\Core\\Src\\sys.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\system_stm32f1xx.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\system_stm32f1xx.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\system_stm32f1xx.d .\\..\\Core\\Src\\system_stm32f1xx.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Core\\Src\\usart.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\usart.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Core\\Src\\usart.d .\\..\\Core\\Src\\usart.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_can.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_can.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_can.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_can.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_uart.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_uart.o --no_depend_system_headers --depend .\\build\\bootloader_hal\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_uart.d .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_uart.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM\\startup_stm32f103xb.s", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_bootloader_hal --cpu Cortex-M3 --li --pd \"__MICROLIB SETA 1\" -g -o .\\build\\bootloader_hal\\.obj\\startup_stm32f103xb.o --depend .\\build\\bootloader_hal\\.obj\\startup_stm32f103xb.d .\\startup_stm32f103xb.s"}]